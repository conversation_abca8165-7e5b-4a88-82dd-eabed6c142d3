import 'package:account_management/account_management.dart';
import 'package:account_management/application/menstrual_cycle_bloc/menstrual_cycle_bloc.dart';
import 'package:account_management/application/period_tracking_watcher_bloc/period_tracking_watcher_bloc.dart';
import 'package:analytics/analytics.dart' hide getIt;
import 'package:auto_route/auto_route.dart';
import 'package:bluetooth/bluetooth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:design_system/design_system.dart';
// import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:juno_plus/pages/dashboard/period_tracking_view_page.dart';
import 'package:juno_plus/pages/settings/settings_page.dart';
import 'package:notifications/application/custom_notification_stream/custom_notification_stream_bloc.dart';
import 'package:notifications/application/notification_bloc/notification_bloc.dart';
import 'package:remote/application/device_control_bloc/device_control_bloc.dart';
import '../helpers.dart';
import '../routing/app_pages.gr.dart';

import 'dashboard/new_dashboard_page.dart';
import 'therapy/therapy_feedback_bottom_sheet.dart';
import 'connectivity/connectivity_mapper.dart';
import 'dashboard/symptom_tracking_page.dart';
import 'dashboard/dashboard_page.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

@RoutePage()
class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final List<String> _tabs = [
    'Calendar',
    'Remote',
    'Tracking',
    'Dashboard',
    'Settings'
  ];
  String _selectedTab = 'Dashboard';
  // late AppUpdateCheck _appUpdateCheck;
  // Create an instance of your controller
  // final TensController _controller = TensController();

  /// Show therapy feedback bottom sheet for the most recent session
  void _showTherapyFeedback(BuildContext context) async {
    try {
      // Get the most recent therapy session from analytics
      final analyticsFacade = getIt<IAnalyticsFacade>();
      final sessionsResult = await analyticsFacade.getStoredSessions();

      sessionsResult.mapBoth(
        onLeft: (String error) {
          print('❌ Error getting therapy sessions: $error');
        },
        onRight: (List<TherapySessionModel> sessions) {
          if (sessions.isNotEmpty) {
            // Get the most recent session (first in list)
            final latestSession = sessions.first;
            print(
                '🎯 Showing feedback for session: ${latestSession.sessionInfo.sessionId}');

            // Show the feedback bottom sheet
            TherapyFeedbackBottomSheet.show(
                context, latestSession.sessionInfo.sessionId);
          } else {
            print('⚠️ No therapy sessions found for feedback');
          }
        },
      );
    } catch (e) {
      print('❌ Error showing therapy feedback: $e');
    }
  }

  @override
  void dispose() {
    // Make sure to dispose the controller when the screen is disposed
    // _controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    // _initializeAppUpdateCheck();
    // _checkForNotifications();

    // Start background scanning for saved devices when app comes to home page
    _initializeBackgroundScanning();
  }

  /// Initialize background scanning for saved devices
  void _initializeBackgroundScanning() {
    // Get the bluetooth service bloc from the app-level provider and start background scanning
    final bluetoothBloc = context.read<BluetoothServiceBloc>();
    // This will start the background scanning process for saved devices
    bluetoothBloc.add(const BluetoothServiceEvent.checkBluetooth());
  }

  // Future<void> _initializeAppUpdateCheck() async {
  //   FirebaseRemoteConfig remoteConfig = await FirebaseRemoteConfig.instance;
  //   _appUpdateCheck = AppUpdateCheck(remoteConfig);
  //   // Check for updates
  //   _appUpdateCheck.checkForUpdate(context);
  // }

  @override
  Widget build(BuildContext context) {
    return PopScope(
        canPop: false,
        child: MultiBlocProvider(
          providers: [
            BlocProvider(
              create: (_) => getIt<NotificationBloc>()
                ..add(NotificationEvent.initialize(context)),
            ),
            BlocProvider(
              create: (_) => getIt<CustomNotificationStreamBloc>()
                ..add(CustomNotificationStreamEvent
                    .loadCustomNotificationStream()),
            ),
            BlocProvider(
              create: (_) => getIt<DeviceControlBloc>(),
            ),
          ],
          child: MultiBlocListener(
            listeners: [
              BlocListener<NotificationBloc, NotificationState>(
                listener: (context, state) {
                  state.maybeMap(
                    success: (_) {
                      SnackBar(
                        content: Text('Notifications initialized successfully'),
                        duration: const Duration(seconds: 2),
                      );
                    },
                    failure: (failure) {
                      SnackBar(
                        content: Text(
                            'Failed to initialize notifications: ${failure.failure}'),
                        duration: const Duration(seconds: 2),
                      );
                    },
                    orElse: () {},
                  );
                },
              ),
              // BlocListener<BluetoothServiceBloc, BluetoothServiceState>(
              //   listener: (context, state) {
              //     state.maybeMap(
              //       bluetoothError: (error) {
              //         SnackBar(
              //           content: Text('Bluetooth Error: ${error.failure}'),
              //           duration: const Duration(seconds: 2),
              //         );
              //       },
              //       orElse: () {},
              //     );
              //   },
              // ),
              BlocListener<MenstrualCycleBloc, MenstrualCycleState>(
                listener: (context, state) {},
              ),
              // Analytics initialization listener
              BlocListener<AnalyticsInitializationBloc,
                  AnalyticsInitializationState>(
                listener: (context, state) {
                  if (state is AnalyticsInitializationLoading) {
                    // Show loading indicator for analytics initialization
                    if (kDebugMode) {
                      print('🔄 Analytics services initializing...');
                    }
                  } else if (state is AnalyticsInitializationSuccess) {
                    // Analytics initialized successfully
                    if (kDebugMode) {
                      print('✅ Analytics initialized successfully');
                      print('📊 Stats: ${state.stats}');

                      // Show detailed stats
                      final stats =
                          state.stats['storageStats'] as Map<String, dynamic>?;
                      if (stats != null) {
                        print('📱 Total sessions: ${stats['totalSessions']}');
                        print(
                            '📤 Unsynced sessions: ${stats['unsyncedSessions']}');
                        print('🌐 Online: ${stats['isOnline']}');
                        print('🔄 Syncing: ${stats['isSyncing']}');
                      }
                    }

                    // Check if most recent session needs feedback
                    getIt<IAnalyticsFacade>().checkMostRecentSession();

                    // Optional: Show success snackbar in debug mode
                    if (kDebugMode) {
                      // ScaffoldMessenger.of(context).showSnackBar(
                      // SnackBar(
                      //   content: Text('Analytics services ready'),
                      //   backgroundColor: Colors.green,
                      //   duration: Duration(seconds: 2),
                      // ),
                      // );
                    }
                  } else if (state is AnalyticsInitializationFailure) {
                    // Analytics initialization failed
                    if (kDebugMode) {
                      print(
                          '❌ Analytics initialization failed: ${state.error}');
                    }

                    // Show error snackbar in debug mode
                    if (kDebugMode) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Analytics initialization failed'),
                          backgroundColor: Colors.red,
                          duration: Duration(seconds: 3),
                          action: SnackBarAction(
                            label: 'Retry',
                            textColor: Colors.white,
                            onPressed: () {
                              // Retry analytics initialization
                              context
                                  .read<AnalyticsInitializationBloc>()
                                  .add(InitializeAnalyticsEvent());
                            },
                          ),
                        ),
                      );
                    }
                  }
                },
              ),
              // Therapy feedback listener
              BlocListener<DeviceControlBloc, DeviceControlState>(
                listener: (context, state) {
                  state.whenOrNull(
                    therapyStateChanged: (isActive) {
                      // When therapy ends (was active, now inactive)
                      if (!isActive) {
                        print(
                            '🎯 Therapy ended, showing feedback bottom sheet');
                        _showTherapyFeedback(context);
                      }
                    },
                  );
                },
              ),
              // Therapy management listener
              // BlocListener<TherapyManagementBloc, TherapyManagementState>(
              //   listener: (context, state) {
              //     state.maybeMap(
              //       loading: (_) {
              //         if (kDebugMode) {
              //           print('🔄 Starting therapy session...');
              //         }
              //       },
              //       therapyStarted: (_) {
              //         if (kDebugMode) {
              //           print('✅ Therapy session started successfully!');
              //           print(
              //               '📊 Session will run for 5 minutes and then auto-end');
              //         }
              //
              //         // Show success snackbar
              //         ScaffoldMessenger.of(context).showSnackBar(
              //           SnackBar(
              //             content:
              //                 Text('Therapy session started! (5 min duration)'),
              //             backgroundColor: Colors.green,
              //             duration: Duration(seconds: 3),
              //           ),
              //         );
              //       },
              //       error: (errorState) {
              //         if (kDebugMode) {
              //           print(
              //               '❌ Failed to start therapy session: ${errorState.message}');
              //         }
              //
              //         // Show error snackbar
              //         ScaffoldMessenger.of(context).showSnackBar(
              //           SnackBar(
              //             content: Text('Failed to start therapy session'),
              //             backgroundColor: Colors.red,
              //             duration: Duration(seconds: 3),
              //           ),
              //         );
              //       },
              //       orElse: () {},
              //     );
              //   },
              // ),
              // listen for custom notification stream
              BlocListener<CustomNotificationStreamBloc,
                  CustomNotificationStreamState>(
                listener: (context, state) {
                  state.maybeMap(
                    successNotification: (notification) {
                      if (notification.notification != null) {
                        print(
                            'Notification received: ${notification.notification}');
                        final notificationModel = notification.notification!;
                        print(
                            'opening notification: ${notificationModel.title}');

                        // Handle therapy feedback notifications specifically
                        print(
                            '🔍 Processing notification: type=${notificationModel.notificationType}, payload=${notificationModel.payload}');

                        // Check for therapy feedback notification first
                        if (notificationModel.notificationType ==
                            'therapy_feedback') {
                          print(
                              '✅ Therapy feedback notification detected, calling analytics');
                          getIt<IAnalyticsFacade>().checkNotificationPayload(
                              notificationModel.payload);
                          return;
                        }

                        // Show generic notification dialog for other types
                        showDialog<void>(
                          context: context,
                          builder: (context) {
                            return AlertDialog(
                              title: Text(
                                  notificationModel.title ?? 'Notification'),
                              content:
                                  Text(notificationModel.body ?? 'No body'),
                              actions: [
                                TextButton(
                                  onPressed: () {
                                    Navigator.of(context).pop();
                                  },
                                  child: const Text('OK'),
                                ),
                              ],
                            );
                          },
                        );
                      }
                    },
                    orElse: () {},
                  );
                },
              ),
              BlocListener<PeriodReminderSettingsBloc,
                  PeriodReminderSettingsState>(
                listener: (context, state) {
                  state.maybeMap(
                    loading: (_) {
                      if (kDebugMode) {
                        print('🔄 Loading period reminder settings...');
                      }
                    },
                    loaded: (loadedState) {
                      if (kDebugMode) {
                        print('✅ Period reminder settings loaded');
                      }
                    },
                    failure: (failureState) {
                      if (kDebugMode) {
                        print(
                            '❌ Failed to load period reminder settings: ${failureState.message}');
                      }
                    },
                    orElse: () {},
                  );
                },
              ),
            ],
            child: StreamBuilder<String>(
              stream: getIt<IAnalyticsFacade>().feedbackTriggerStream,
              builder: (context, snapshot) {
                // When a session ID is emitted, check if feedback is still needed before showing bottom sheet
                if (snapshot.hasData && snapshot.data != null) {
                  WidgetsBinding.instance.addPostFrameCallback((_) async {
                    final sessionId = snapshot.data!;
                    print(
                        '🎯 Feedback trigger received for session: $sessionId');

                    // Double-check if session still needs feedback before showing bottom sheet
                    try {
                      final analyticsFacade = getIt<IAnalyticsFacade>();
                      final sessionsResult =
                          await analyticsFacade.getStoredSessions();

                      sessionsResult.mapBoth(
                        onLeft: (error) {
                          print('❌ Error getting sessions: $error');
                          // Fallback: show bottom sheet anyway
                          TherapyFeedbackBottomSheet.show(context, sessionId);
                        },
                        onRight: (sessions) {
                          final session = sessions
                              .where(
                                  (s) => s.sessionInfo.sessionId == sessionId)
                              .firstOrNull;

                          if (session != null &&
                              session.status == 'completed' &&
                              session.feedback?.feedbackCompleted != true) {
                            print(
                                '✅ Session still needs feedback, showing bottom sheet');
                            TherapyFeedbackBottomSheet.show(context, sessionId);
                          } else {
                            print(
                                '🚫 Session no longer needs feedback, skipping bottom sheet');
                          }
                        },
                      );
                    } catch (e) {
                      print('❌ Error checking session feedback status: $e');
                      // Fallback: show bottom sheet anyway
                      TherapyFeedbackBottomSheet.show(context, sessionId);
                    }
                  });
                }

                return Scaffold(
                    extendBody: true,
                    backgroundColor: Colors.transparent,
                    bottomNavigationBar: Padding(
                      padding: const EdgeInsets.only(
                          left: 20.0, right: 20.0, bottom: 20.0, top: 20.0),
                      child: Container(
                        width: 1.0.sw,
                        height: .18.sw,
                        decoration: BoxDecoration(
                          color: AppTheme.primaryColor,
                          borderRadius:
                              const BorderRadius.all(Radius.circular(40)),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            GestureDetector(
                                key: const Key('calendar'),
                                onTap: () {
                                  context.router
                                      .push(PeriodTrackingViewRoute());
                                },
                                child: Icon(
                                  Icons.calendar_today_rounded,
                                  size: 28,
                                  color: const Color(0xffCFB4FE),
                                )),
                            GestureDetector(
                              key: const Key('remote'),
                              onTap: () {
                                setState(() {
                                  _selectedTab = _tabs[1];
                                });

                                // setState(() {
                                //   context.router.push(RemoteOneRoute());
                                // });
                              },
                              child: SvgPicture.asset(
                                'assets/home/<USER>',
                                height: 30,
                                width: 30,
                                colorFilter: ColorFilter.mode(
                                  _selectedTab == 'Remote'
                                      ? Colors.white
                                      : const Color(0xffCFB4FE),
                                  BlendMode.srcIn,
                                ),
                              ),
                            ),
                      
                            GestureDetector(
                              onTap: () {
                                setState(() {
                                  _selectedTab = _tabs[3];
                                });
                              },
                              child: SvgPicture.asset(
                                'assets/home/<USER>',
                                height: 25,
                                width: 25,
                                colorFilter: ColorFilter.mode(
                                  _selectedTab == 'Dashboard'
                                      ? Colors.white
                                      : const Color(0xffCFB4FE),
                                  BlendMode.srcIn,
                                ),
                              ),
                            ),
                            GestureDetector(
                              key: const Key('settings'),
                              onTap: () {
                                setState(() {
                                  _selectedTab = _tabs[4];
                                });
                                print('Settings clicked');

                                // DEBUG: Populate dummy symptoms
                                if (kDebugMode) {
                                 
                                }

                                // DEBUG: Force resync all sessions to test sync issue
                                // if (kDebugMode) {
                                //   // _debugForceResyncSessions(context);
                                // }
                              },
                              child: SvgPicture.asset(
                                'assets/home/<USER>',
                                height: 28,
                                width: 28,
                                colorFilter: ColorFilter.mode(
                                  _selectedTab == 'Settings'
                                      ? Colors.white
                                      : const Color(0xffCFB4FE),
                                  BlendMode.srcIn,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    body: _selectedTab == 'Dashboard'
                        ? NewDashboardPage()
                        : _selectedTab == 'Remote'
                            ? ConnectivityMapper()
                            : _selectedTab == 'Settings'
                                ? SettingsPage()
                                : PeriodTrackingViewPage() // Default to calendar for Tracking tab

                    );
              },
            ),
          ),
        ));
  }}

  /// Debug method to force resync all sessions
  // void _debugForceResyncSessions(BuildContext context) async {
  //   try {
  //     print('🔍 DEBUG: Force resyncing all sessions...');
  //
  //     // Get analytics facade from context
  //     final analyticsFacade = getIt<IAnalyticsFacade>();
  //
  //     // Force resync all sessions
  //     final result = await analyticsFacade.forceResyncAllSessions();
  //
  //     result.mapBoth(
  //       onLeft: (error) {
  //         print('❌ DEBUG: Force resync failed: $error');
  //         if (kDebugMode) {
  //           ScaffoldMessenger.of(context).showSnackBar(
  //             SnackBar(
  //               content: Text('Force resync failed: $error'),
  //               backgroundColor: Colors.red,
  //               duration: Duration(seconds: 5),
  //             ),
  //           );
  //         }
  //       },
  //       onRight: (_) {
  //         print('✅ DEBUG: Force resync completed successfully');
  //         if (kDebugMode) {
  //           ScaffoldMessenger.of(context).showSnackBar(
  //             SnackBar(
  //               content: Text('Force resync completed - check console logs'),
  //               backgroundColor: Colors.green,
  //               duration: Duration(seconds: 3),
  //             ),
  //           );
  //         }
  //       },
  //     );
  //   } catch (e) {
  //     print('❌ DEBUG: Exception during force resync: $e');
  //   }
  // }

  // void _showSymptomTrackingForToday(BuildContext context) {
  //   // Show the symptom tracking bottom sheet with proper bloc providers
  //   showModalBottomSheet<void>(
  //     context: context,
  //     isScrollControlled: true,
  //     backgroundColor: Colors.transparent,
  //     builder: (context) => Container(
  //       height: .85.sh,
  //       decoration: BoxDecoration(
  //         color: Color(0xffFAF2DF),
  //         borderRadius: BorderRadius.only(
  //           topLeft: Radius.circular(32),
  //           topRight: Radius.circular(32),
  //         ),
  //       ),
  //       clipBehavior: Clip.hardEdge,
  //       child: MultiBlocProvider(
  //         providers: [
  //           BlocProvider<ManagePeriodTrackingBloc>(
  //             create: (context) => getIt<ManagePeriodTrackingBloc>()
  //   )
  //           ,
  //           BlocProvider<PeriodTrackingWatcherBloc>(
  //             create: (context) => getIt<PeriodTrackingWatcherBloc>()
  //               ..add(const PeriodTrackingWatcherEvent.watchAllStarted()),
  //           ),
  //         ],
  //         child: SymptomTrackingPage(),
  //       ),
  //     ),
  //   );
  // }

  /// DEBUG: Function to populate dummy symptoms in Firestore with colors
 
   